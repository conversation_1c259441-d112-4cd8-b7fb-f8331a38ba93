# CTINT 敏感信息截图保护系统

这是一个 iOS SwiftUI 应用程序，实现了敏感信息的截图保护功能。当用户尝试截屏时，系统会自动显示"CTINT机密"覆盖层，保护敏感信息不被截图。

## 功能特性

### 🛡️ 核心保护功能
- **截图检测**: 自动检测用户截图行为
- **覆盖层保护**: 截图时显示黑色背景 + "CTINT机密" 文字
- **屏幕录制检测**: 检测并警告屏幕录制状态
- **震动反馈**: 截图时提供触觉反馈提醒

### ⚙️ 可配置选项
- **保护级别**: 低/中/高三个级别，影响覆盖层透明度和显示时长
- **显示时长**: 可调节覆盖层显示时间（1-10秒）
- **开关控制**: 可以完全启用/禁用保护功能
- **反馈设置**: 可控制震动反馈的开启/关闭

### 📱 用户界面
- **主界面**: 展示模拟的敏感信息卡片
- **设置界面**: 完整的保护配置选项
- **状态指示**: 实时显示屏幕录制状态
- **敏感内容标记**: 自动对敏感区域应用保护

## 技术实现

### 架构组件

1. **ScreenshotDetector.swift**
   - 监听系统截图通知
   - 监听屏幕录制状态变化
   - 提供截图事件的响应机制

2. **PrivacyProtectionManager.swift**
   - 管理保护配置和策略
   - 提供不同保护级别
   - 记录和日志截图事件

3. **ConfidentialOverlayView.swift**
   - 实现覆盖层视图
   - 动态调整透明度和显示效果
   - 提供动画和过渡效果

4. **ContentView.swift**
   - 主界面集成所有保护功能
   - 展示敏感信息示例
   - 提供设置入口

### 保护级别说明

| 级别 | 覆盖层透明度 | 显示时长 | 适用场景 |
|------|-------------|----------|----------|
| 低   | 70%         | 2秒      | 一般敏感信息 |
| 中   | 85%         | 3秒      | 重要敏感信息 |
| 高   | 95%         | 5秒      | 极度敏感信息 |

## 使用方法

### 基本使用
1. 启动应用程序
2. 查看模拟的敏感信息
3. 尝试截图测试保护功能
4. 观察"CTINT机密"覆盖层的显示

### 配置设置
1. 点击右上角"设置"按钮
2. 调整保护级别和显示时长
3. 开启/关闭各项保护功能
4. 查看当前屏幕录制状态

### 集成到现有项目

```swift
// 1. 添加保护管理器
@StateObject private var protectionManager = PrivacyProtectionManager.shared
@StateObject private var screenshotDetector = ScreenshotDetector.shared

// 2. 为敏感视图添加保护
SensitiveView()
    .sensitiveContent()

// 3. 添加覆盖层
ZStack {
    // 您的内容
    ContentView()
    
    // 保护覆盖层
    if protectionManager.isProtectionEnabled {
        ConfidentialOverlayView(isVisible: screenshotDetector.screenshotTaken)
    }
}
```

## 测试

项目包含完整的单元测试，验证：
- 截图检测器初始化
- 保护管理器配置
- 不同保护级别的参数
- 保护功能的开关控制

运行测试：
```bash
# 在 Xcode 中按 Cmd+U 运行测试
# 或使用命令行
xcodebuild test -scheme cdss-demo -destination 'platform=iOS Simulator,name=iPhone 15'
```

## 注意事项

### 系统限制
- iOS 系统不允许应用程序直接修改用户的截图文件
- 本方案通过在截图时显示覆盖层来实现保护效果
- 覆盖层会在截图后短暂显示，提醒用户截图行为被检测

### 安全建议
- 对于极度敏感的信息，建议结合其他安全措施
- 定期检查和更新保护策略
- 监控截图事件日志，分析安全风险

### 兼容性
- 支持 iOS 14.0+
- 支持 iPhone 和 iPad
- 支持深色/浅色模式自动适配

## 开发者信息

- **项目名称**: CTINT 敏感信息截图保护系统
- **开发者**: kit.yeung
- **创建时间**: 2025年4月21日
- **更新时间**: 2025年7月31日
- **技术栈**: SwiftUI, UIKit, Combine

## 许可证

本项目仅供学习和演示使用。在生产环境中使用前，请确保符合相关法律法规和隐私政策要求。
