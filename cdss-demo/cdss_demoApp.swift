//
//  cdss_demoApp.swift
//  cdss-demo
//
//  Created by kit.yeung on 2025/4/21.
//

import SwiftUI

@main
struct cdss_demoApp: App {
    // 初始化保护系统
    @StateObject private var screenshotDetector = ScreenshotDetector.shared
    @StateObject private var protectionManager = PrivacyProtectionManager.shared

    init() {
        // 应用程序启动时的配置
        print("CTINT 敏感信息保护系统已启动")
    }

    var body: some Scene {
        WindowGroup {
            ContentView()
                .onAppear {
                    // 确保保护系统已初始化
                    _ = screenshotDetector
                    _ = protectionManager
                }
        }
    }
}
