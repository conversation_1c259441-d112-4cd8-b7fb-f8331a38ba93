//
//  ScreenshotDetector.swift
//  cdss-demo
//
//  Created by kit.yeung on 2025/7/31.
//

import SwiftUI
import UIKit

class ScreenshotDetector: ObservableObject {
    static let shared = ScreenshotDetector()
    
    @Published var screenshotTaken = false
    @Published var screenRecording = false
    
    private init() {
        setupNotifications()
    }
    
    private func setupNotifications() {
        // 监听截图通知
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(screenshotTakenNotification),
            name: UIApplication.userDidTakeScreenshotNotification,
            object: nil
        )
        
        // 监听屏幕录制状态变化
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(screenCaptureDidChange),
            name: UIScreen.capturedDidChangeNotification,
            object: nil
        )
    }
    
    @objc private func screenshotTakenNotification() {
        DispatchQueue.main.async {
            let protectionManager = PrivacyProtectionManager.shared

            // 只有在保护启用时才显示覆盖层
            guard protectionManager.isProtectionEnabled else { return }

            self.screenshotTaken = true

            // 记录截图事件
            protectionManager.logScreenshotEvent()

            // 添加震动反馈
            if protectionManager.enableHapticFeedback {
                let impactFeedback = UIImpactFeedbackGenerator(style: .heavy)
                impactFeedback.impactOccurred()
            }

            // 根据保护级别设置显示时长
            let config = protectionManager.getCurrentProtectionConfig()
            DispatchQueue.main.asyncAfter(deadline: .now() + config.duration) {
                self.screenshotTaken = false
            }
        }
    }
    
    @objc private func screenCaptureDidChange() {
        DispatchQueue.main.async {
            self.screenRecording = UIScreen.main.isCaptured
        }
    }
    
    deinit {
        NotificationCenter.default.removeObserver(self)
    }
}
