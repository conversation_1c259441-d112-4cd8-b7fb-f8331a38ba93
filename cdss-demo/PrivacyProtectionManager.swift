//
//  PrivacyProtectionManager.swift
//  cdss-demo
//
//  Created by kit.yeung on 2025/7/31.
//

import SwiftUI
import UIKit

class PrivacyProtectionManager: ObservableObject {
    static let shared = PrivacyProtectionManager()
    
    @Published var isProtectionEnabled = true
    @Published var overlayDisplayDuration: TimeInterval = 3.0
    @Published var showScreenRecordingWarning = true
    @Published var enableHapticFeedback = true
    
    // 保护级别
    enum ProtectionLevel: String, CaseIterable {
        case low = "低"
        case medium = "中"
        case high = "高"
        
        var overlayOpacity: Double {
            switch self {
            case .low: return 0.7
            case .medium: return 0.85
            case .high: return 0.95
            }
        }
        
        var displayDuration: TimeInterval {
            switch self {
            case .low: return 2.0
            case .medium: return 3.0
            case .high: return 5.0
            }
        }
    }
    
    @Published var protectionLevel: ProtectionLevel = .medium
    
    private init() {}
    
    // 记录截图事件
    func logScreenshotEvent() {
        let timestamp = Date()
        print("🚨 截图检测 - 时间: \(timestamp)")
        
        // 这里可以添加更多的日志记录逻辑
        // 比如发送到服务器、本地存储等
    }
    
    // 获取当前保护配置
    func getCurrentProtectionConfig() -> (opacity: Double, duration: TimeInterval) {
        return (
            opacity: protectionLevel.overlayOpacity,
            duration: protectionLevel.displayDuration
        )
    }
}

// 敏感信息标记修饰符
struct SensitiveContent: ViewModifier {
    @StateObject private var protectionManager = PrivacyProtectionManager.shared
    
    func body(content: Content) -> some View {
        content
            .overlay(
                // 在屏幕录制时显示模糊效果
                Rectangle()
                    .fill(.ultraThinMaterial)
                    .opacity(protectionManager.showScreenRecordingWarning && UIScreen.main.isCaptured ? 0.8 : 0)
                    .animation(.easeInOut(duration: 0.3), value: UIScreen.main.isCaptured)
            )
    }
}

extension View {
    func sensitiveContent() -> some View {
        modifier(SensitiveContent())
    }
}

// 保护设置视图
struct ProtectionSettingsView: View {
    @StateObject private var protectionManager = PrivacyProtectionManager.shared
    
    var body: some View {
        NavigationView {
            Form {
                Section("基本设置") {
                    Toggle("启用截图保护", isOn: $protectionManager.isProtectionEnabled)
                    Toggle("屏幕录制警告", isOn: $protectionManager.showScreenRecordingWarning)
                    Toggle("震动反馈", isOn: $protectionManager.enableHapticFeedback)
                }
                
                Section("保护级别") {
                    Picker("保护级别", selection: $protectionManager.protectionLevel) {
                        ForEach(PrivacyProtectionManager.ProtectionLevel.allCases, id: \.self) { level in
                            Text(level.rawValue).tag(level)
                        }
                    }
                    .pickerStyle(SegmentedPickerStyle())
                }
                
                Section("显示时长") {
                    HStack {
                        Text("覆盖层显示时长")
                        Spacer()
                        Text("\(protectionManager.overlayDisplayDuration, specifier: "%.1f")秒")
                            .foregroundColor(.secondary)
                    }
                    
                    Slider(
                        value: $protectionManager.overlayDisplayDuration,
                        in: 1.0...10.0,
                        step: 0.5
                    )
                }
                
                Section("当前状态") {
                    HStack {
                        Text("屏幕录制状态")
                        Spacer()
                        Text(UIScreen.main.isCaptured ? "录制中" : "正常")
                            .foregroundColor(UIScreen.main.isCaptured ? .red : .green)
                    }
                }
            }
            .navigationTitle("隐私保护设置")
            .navigationBarTitleDisplayMode(.inline)
        }
    }
}

#Preview {
    ProtectionSettingsView()
}
