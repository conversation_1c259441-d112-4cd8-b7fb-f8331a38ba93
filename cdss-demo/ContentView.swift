//
//  ContentView.swift
//  cdss-demo
//
//  Created by kit.yeung on 2025/4/21.
//

import SwiftUI

struct ContentView: View {
    @StateObject private var screenshotDetector = ScreenshotDetector.shared
    @StateObject private var protectionManager = PrivacyProtectionManager.shared
    @State private var showingSettings = false

    var body: some View {
        NavigationView {
            ZStack {
                // 主要内容
                VStack(spacing: 30) {
                    // 模拟敏感信息区域
                    VStack(spacing: 20) {
                        SensitiveInfoCard(
                            title: "机密文档",
                            content: "这是一些敏感信息内容\n账号: <EMAIL>\n密码: ********\n项目代号: ALPHA-2025"
                        )
                        .sensitiveContent()

                        SensitiveInfoCard(
                            title: "财务数据",
                            content: "Q4营收: ¥1,250,000\n利润率: 23.5%\n预算分配: 机密"
                        )
                        .sensitiveContent()

                        SensitiveInfoCard(
                            title: "客户信息",
                            content: "VIP客户列表\n联系方式: 保密\n合同金额: ¥5,000,000"
                        )
                        .sensitiveContent()
                    }

                    // 屏幕录制指示器
                    ScreenRecordingIndicator(isRecording: screenshotDetector.screenRecording)

                    Spacer()

                    // 提示信息
                    VStack(spacing: 10) {
                        Text("📱 尝试截图以测试保护功能")
                            .font(.headline)
                            .foregroundColor(.secondary)

                        Text("当您截图时，将显示保护层")
                            .font(.caption)
                            .foregroundColor(.secondary)

                        Text("保护级别: \(protectionManager.protectionLevel.rawValue)")
                            .font(.caption2)
                            .padding(.horizontal, 12)
                            .padding(.vertical, 4)
                            .background(Color.blue.opacity(0.1))
                            .foregroundColor(.blue)
                            .cornerRadius(8)
                    }
                }
                .padding()

                // 截图保护覆盖层
                if protectionManager.isProtectionEnabled {
                    ConfidentialOverlayView(isVisible: screenshotDetector.screenshotTaken)
                }
            }
            .navigationTitle("CTINT 机密系统")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("设置") {
                        showingSettings = true
                    }
                }
            }
            .sheet(isPresented: $showingSettings) {
                ProtectionSettingsView()
            }
        }
    }
}

struct SensitiveInfoCard: View {
    let title: String
    let content: String

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: "lock.shield.fill")
                    .foregroundColor(.red)
                Text(title)
                    .font(.headline)
                    .fontWeight(.semibold)
                Spacer()
                Text("机密")
                    .font(.caption)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(Color.red.opacity(0.1))
                    .foregroundColor(.red)
                    .cornerRadius(4)
            }

            Text(content)
                .font(.body)
                .foregroundColor(.primary)
                .multilineTextAlignment(.leading)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
        )
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(Color.red.opacity(0.3), lineWidth: 1)
        )
    }
}

#Preview {
    ContentView()
}
