//
//  ConfidentialOverlayView.swift
//  cdss-demo
//
//  Created by kit.yeung on 2025/7/31.
//

import SwiftUI

struct ConfidentialOverlayView: View {
    let isVisible: Bool
    @StateObject private var protectionManager = PrivacyProtectionManager.shared

    var body: some View {
        if isVisible {
            ZStack {
                // 黑色半透明背景，根据保护级别调整透明度
                Color.black
                    .opacity(protectionManager.getCurrentProtectionConfig().opacity)
                    .ignoresSafeArea(.all)
                
                VStack(spacing: 20) {
                    // 警告图标
                    Image(systemName: "exclamationmark.triangle.fill")
                        .font(.system(size: 60))
                        .foregroundColor(.red)
                    
                    // 机密文字
                    Text("CTINT机密")
                        .font(.system(size: 36, weight: .bold, design: .default))
                        .foregroundColor(.white)
                        .shadow(color: .red, radius: 2, x: 0, y: 0)
                    
                    // 提示文字
                    Text("检测到截图行为")
                        .font(.system(size: 18, weight: .medium))
                        .foregroundColor(.white.opacity(0.8))
                    
                    Text("敏感信息受到保护")
                        .font(.system(size: 16))
                        .foregroundColor(.white.opacity(0.6))
                }
                .scaleEffect(isVisible ? 1.0 : 0.8)
                .opacity(isVisible ? 1.0 : 0.0)
            }
            .transition(.asymmetric(
                insertion: .scale.combined(with: .opacity),
                removal: .opacity
            ))
            .animation(.easeInOut(duration: 0.3), value: isVisible)
        }
    }
}

struct ScreenRecordingIndicator: View {
    let isRecording: Bool
    
    var body: some View {
        if isRecording {
            HStack {
                Image(systemName: "record.circle.fill")
                    .foregroundColor(.red)
                    .font(.system(size: 16))
                
                Text("屏幕录制中 - 敏感信息受保护")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.red)
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 8)
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(Color.red.opacity(0.1))
                    .stroke(Color.red.opacity(0.3), lineWidth: 1)
            )
            .transition(.move(edge: .top).combined(with: .opacity))
            .animation(.easeInOut(duration: 0.3), value: isRecording)
        }
    }
}

#Preview {
    ZStack {
        Color.blue.ignoresSafeArea()
        ConfidentialOverlayView(isVisible: true)
    }
}
