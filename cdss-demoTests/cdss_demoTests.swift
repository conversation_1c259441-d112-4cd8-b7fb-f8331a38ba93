//
//  cdss_demoTests.swift
//  cdss-demoTests
//
//  Created by kit.yeung on 2025/4/21.
//

import Testing
@testable import cdss_demo

struct cdss_demoTests {

    @Test func testScreenshotDetectorInitialization() async throws {
        let screenshotDetector = ScreenshotDetector.shared
        #expect(screenshotDetector.screenshotTaken == false)
        #expect(screenshotDetector.screenRecording == false)
    }

    @Test func testPrivacyProtectionManagerInitialization() async throws {
        let protectionManager = PrivacyProtectionManager.shared
        #expect(protectionManager.isProtectionEnabled == true)
        #expect(protectionManager.protectionLevel == .medium)
        #expect(protectionManager.enableHapticFeedback == true)
    }

    @Test func testProtectionLevels() async throws {
        let protectionManager = PrivacyProtectionManager.shared

        // 测试低级别保护
        protectionManager.protectionLevel = .low
        var config = protectionManager.getCurrentProtectionConfig()
        #expect(config.opacity == 0.7)
        #expect(config.duration == 2.0)

        // 测试中级别保护
        protectionManager.protectionLevel = .medium
        config = protectionManager.getCurrentProtectionConfig()
        #expect(config.opacity == 0.85)
        #expect(config.duration == 3.0)

        // 测试高级别保护
        protectionManager.protectionLevel = .high
        config = protectionManager.getCurrentProtectionConfig()
        #expect(config.opacity == 0.95)
        #expect(config.duration == 5.0)
    }

    @Test func testProtectionToggle() async throws {
        let protectionManager = PrivacyProtectionManager.shared

        // 测试关闭保护
        protectionManager.isProtectionEnabled = false
        #expect(protectionManager.isProtectionEnabled == false)

        // 测试开启保护
        protectionManager.isProtectionEnabled = true
        #expect(protectionManager.isProtectionEnabled == true)
    }

}
